"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx":
/*!***************************************************!*\
  !*** ./src/components/DynamicStatusIndicator.tsx ***!
  \***************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ DynamicStatusIndicator),\n/* harmony export */   useStatusProgression: () => (/* binding */ useStatusProgression)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! styled-jsx/style */ \"(app-pages-browser)/./node_modules/styled-jsx/style.js\");\n/* harmony import */ var styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(styled_jsx_style__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/RocketLaunchIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/MagnifyingGlassIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ArrowPathIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/LightBulbIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/StarIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/UserGroupIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CpuChipIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/BoltIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CommandLineIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/FireIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/PencilIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/SparklesIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ShieldCheckIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CheckCircleIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/CogIcon.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowPathIcon,BoltIcon,CheckCircleIcon,ClipboardDocumentListIcon,CogIcon,CommandLineIcon,CpuChipIcon,FireIcon,LightBulbIcon,MagnifyingGlassIcon,PencilIcon,RocketLaunchIcon,ShieldCheckIcon,SparklesIcon,StarIcon,UserGroupIcon!=!@heroicons/react/24/outline */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/outline/esm/ClipboardDocumentListIcon.js\");\n/* harmony import */ var _barrel_optimize_names_TagIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=TagIcon!=!@heroicons/react/24/solid */ \"(app-pages-browser)/./node_modules/@heroicons/react/24/solid/esm/TagIcon.js\");\n/* __next_internal_client_entry_do_not_use__ default,useStatusProgression auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\n\n// Additional icons for orchestration\n\nconst STATUS_CONFIGS = {\n    initializing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"],\n        text: 'Initializing',\n        description: 'Starting up systems',\n        bgColor: 'bg-gradient-to-r from-slate-50 to-gray-50',\n        iconColor: 'text-slate-600',\n        borderColor: 'border-slate-200/60',\n        glowColor: 'shadow-slate-200/50',\n        gradientFrom: 'from-slate-400',\n        gradientTo: 'to-gray-400',\n        duration: 200\n    },\n    analyzing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"],\n        text: 'Analyzing',\n        description: 'Understanding your request',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 300\n    },\n    routing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"],\n        text: 'Smart routing',\n        description: 'Finding optimal path',\n        bgColor: 'bg-gradient-to-r from-indigo-50 to-purple-50',\n        iconColor: 'text-indigo-600',\n        borderColor: 'border-indigo-200/60',\n        glowColor: 'shadow-indigo-200/50',\n        gradientFrom: 'from-indigo-400',\n        gradientTo: 'to-purple-400',\n        duration: 400\n    },\n    complexity_analysis: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Evaluating request depth',\n        bgColor: 'bg-gradient-to-r from-amber-50 to-yellow-50',\n        iconColor: 'text-amber-600',\n        borderColor: 'border-amber-200/60',\n        glowColor: 'shadow-amber-200/50',\n        gradientFrom: 'from-amber-400',\n        gradientTo: 'to-yellow-400',\n        duration: 500\n    },\n    role_classification: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"],\n        text: 'Assembling specialists',\n        description: 'Building expert team',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 600\n    },\n    agent_mode_complexity: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"],\n        text: 'Analyzing complexity',\n        description: 'Classifying task complexity (1-5)',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-red-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-red-400',\n        duration: 500\n    },\n    agent_mode_selection: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"],\n        text: 'Selecting agents',\n        description: 'Choosing optimal agent team',\n        bgColor: 'bg-gradient-to-r from-cyan-50 to-blue-50',\n        iconColor: 'text-cyan-600',\n        borderColor: 'border-cyan-200/60',\n        glowColor: 'shadow-cyan-200/50',\n        gradientFrom: 'from-cyan-400',\n        gradientTo: 'to-blue-400',\n        duration: 400\n    },\n    agent_mode_collaboration: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"],\n        text: 'Agent collaboration',\n        description: 'Multi-agent processing',\n        bgColor: 'bg-gradient-to-r from-violet-50 to-purple-50',\n        iconColor: 'text-violet-600',\n        borderColor: 'border-violet-200/60',\n        glowColor: 'shadow-violet-200/50',\n        gradientFrom: 'from-violet-400',\n        gradientTo: 'to-purple-400',\n        duration: 800\n    },\n    preparing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"],\n        text: 'Preparing',\n        description: 'Setting up processing',\n        bgColor: 'bg-gradient-to-r from-orange-50 to-amber-50',\n        iconColor: 'text-orange-600',\n        borderColor: 'border-orange-200/60',\n        glowColor: 'shadow-orange-200/50',\n        gradientFrom: 'from-orange-400',\n        gradientTo: 'to-amber-400',\n        duration: 300\n    },\n    connecting: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_11__[\"default\"],\n        text: 'Connecting',\n        description: 'Establishing AI link',\n        bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n        iconColor: 'text-rose-600',\n        borderColor: 'border-rose-200/60',\n        glowColor: 'shadow-rose-200/50',\n        gradientFrom: 'from-rose-400',\n        gradientTo: 'to-pink-400',\n        duration: 400\n    },\n    generating: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_12__[\"default\"],\n        text: 'Thinking deeply',\n        description: 'AI processing in progress',\n        bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n        iconColor: 'text-emerald-600',\n        borderColor: 'border-emerald-200/60',\n        glowColor: 'shadow-emerald-200/50',\n        gradientFrom: 'from-emerald-400',\n        gradientTo: 'to-teal-400',\n        duration: 800\n    },\n    typing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_13__[\"default\"],\n        text: 'Streaming response',\n        description: 'Delivering your answer',\n        bgColor: 'bg-gradient-to-r from-green-50 to-emerald-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-emerald-400'\n    },\n    finalizing: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_14__[\"default\"],\n        text: 'Finalizing',\n        description: 'Adding finishing touches',\n        bgColor: 'bg-gradient-to-r from-teal-50 to-cyan-50',\n        iconColor: 'text-teal-600',\n        borderColor: 'border-teal-200/60',\n        glowColor: 'shadow-teal-200/50',\n        gradientFrom: 'from-teal-400',\n        gradientTo: 'to-cyan-400',\n        duration: 200\n    },\n    complete: {\n        icon: _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_15__[\"default\"],\n        text: 'Complete',\n        description: 'Response delivered',\n        bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n        iconColor: 'text-green-600',\n        borderColor: 'border-green-200/60',\n        glowColor: 'shadow-green-200/50',\n        gradientFrom: 'from-green-400',\n        gradientTo: 'to-lime-400',\n        duration: 100\n    }\n};\nfunction DynamicStatusIndicator(param) {\n    let { currentStage, isStreaming = false, className = '', onStageChange, orchestrationStatus } = param;\n    _s();\n    const [displayStage, setDisplayStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(currentStage);\n    const [isTransitioning, setIsTransitioning] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(false);\n    const [orchestrationColorIndex, setOrchestrationColorIndex] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(0);\n    const [lastOrchestrationStatus, setLastOrchestrationStatus] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('');\n    const [spinnerSpeed, setSpinnerSpeed] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)(1); // 1 = normal speed, higher = faster\n    // Color palette for orchestration progress cycling\n    const orchestrationColors = [\n        {\n            bgColor: 'bg-gradient-to-r from-blue-50 to-indigo-50',\n            iconColor: 'text-blue-600',\n            borderColor: 'border-blue-200/60',\n            glowColor: 'shadow-blue-200/50',\n            gradientFrom: 'from-blue-400',\n            gradientTo: 'to-indigo-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-purple-50 to-violet-50',\n            iconColor: 'text-purple-600',\n            borderColor: 'border-purple-200/60',\n            glowColor: 'shadow-purple-200/50',\n            gradientFrom: 'from-purple-400',\n            gradientTo: 'to-violet-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-indigo-50 to-blue-50',\n            iconColor: 'text-indigo-600',\n            borderColor: 'border-indigo-200/60',\n            glowColor: 'shadow-indigo-200/50',\n            gradientFrom: 'from-indigo-400',\n            gradientTo: 'to-blue-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-cyan-50 to-teal-50',\n            iconColor: 'text-cyan-600',\n            borderColor: 'border-cyan-200/60',\n            glowColor: 'shadow-cyan-200/50',\n            gradientFrom: 'from-cyan-400',\n            gradientTo: 'to-teal-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-teal-50 to-emerald-50',\n            iconColor: 'text-teal-600',\n            borderColor: 'border-teal-200/60',\n            glowColor: 'shadow-teal-200/50',\n            gradientFrom: 'from-teal-400',\n            gradientTo: 'to-emerald-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-green-50 to-lime-50',\n            iconColor: 'text-green-600',\n            borderColor: 'border-green-200/60',\n            glowColor: 'shadow-green-200/50',\n            gradientFrom: 'from-green-400',\n            gradientTo: 'to-lime-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-yellow-50 to-amber-50',\n            iconColor: 'text-yellow-600',\n            borderColor: 'border-yellow-200/60',\n            glowColor: 'shadow-yellow-200/50',\n            gradientFrom: 'from-yellow-400',\n            gradientTo: 'to-amber-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-orange-50 to-red-50',\n            iconColor: 'text-orange-600',\n            borderColor: 'border-orange-200/60',\n            glowColor: 'shadow-orange-200/50',\n            gradientFrom: 'from-orange-400',\n            gradientTo: 'to-red-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-rose-50 to-pink-50',\n            iconColor: 'text-rose-600',\n            borderColor: 'border-rose-200/60',\n            glowColor: 'shadow-rose-200/50',\n            gradientFrom: 'from-rose-400',\n            gradientTo: 'to-pink-400'\n        },\n        {\n            bgColor: 'bg-gradient-to-r from-emerald-50 to-teal-50',\n            iconColor: 'text-emerald-600',\n            borderColor: 'border-emerald-200/60',\n            glowColor: 'shadow-emerald-200/50',\n            gradientFrom: 'from-emerald-400',\n            gradientTo: 'to-teal-400'\n        }\n    ];\n    // Use orchestration colors if in orchestration mode, otherwise use default config\n    const isOrchestrationMode = !!orchestrationStatus;\n    const currentOrchestrationColor = orchestrationColors[orchestrationColorIndex % orchestrationColors.length];\n    // Get appropriate icon based on orchestration status\n    const getOrchestrationIcon = (status)=>{\n        if (status.includes('🔍') || status.includes('detected')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_4__[\"default\"];\n        if (status.includes('✅') || status.includes('complete')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_16__[\"default\"];\n        if (status.includes('🎯') || status.includes('Selected')) return _barrel_optimize_names_TagIcon_heroicons_react_24_solid__WEBPACK_IMPORTED_MODULE_17__[\"default\"];\n        if (status.includes('🏗️') || status.includes('workflow')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_18__[\"default\"];\n        if (status.includes('🤖') || status.includes('agent')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_8__[\"default\"];\n        if (status.includes('👑') || status.includes('supervisor')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_7__[\"default\"]; // Use StarIcon for supervisor\n        if (status.includes('📋') || status.includes('Planning')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_19__[\"default\"];\n        if (status.includes('🚀') || status.includes('starting')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_3__[\"default\"];\n        if (status.includes('🔄') || status.includes('synthesizing')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_5__[\"default\"];\n        // Agent Mode specific icons\n        if (status.includes('🧠') || status.includes('complexity')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_6__[\"default\"];\n        if (status.includes('👥') || status.includes('collaboration')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        if (status.includes('⚡') || status.includes('selecting')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_10__[\"default\"];\n        if (status.includes('Agent Mode')) return _barrel_optimize_names_ArrowPathIcon_BoltIcon_CheckCircleIcon_ClipboardDocumentListIcon_CogIcon_CommandLineIcon_CpuChipIcon_FireIcon_LightBulbIcon_MagnifyingGlassIcon_PencilIcon_RocketLaunchIcon_ShieldCheckIcon_SparklesIcon_StarIcon_UserGroupIcon_heroicons_react_24_outline__WEBPACK_IMPORTED_MODULE_9__[\"default\"];\n        return STATUS_CONFIGS[displayStage].icon; // fallback\n    };\n    const config = isOrchestrationMode ? {\n        ...STATUS_CONFIGS[displayStage],\n        ...currentOrchestrationColor,\n        icon: getOrchestrationIcon(orchestrationStatus)\n    } : STATUS_CONFIGS[displayStage];\n    const Icon = config.icon;\n    // Handle stage transitions with speed-up animation\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (currentStage !== displayStage) {\n                console.log(\"\\uD83C\\uDFAF DynamicStatusIndicator: \".concat(displayStage, \" → \").concat(currentStage));\n                setIsTransitioning(true);\n                // Speed up animation during transition\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setDisplayStage(currentStage);\n                        setIsTransitioning(false);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(currentStage);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 200); // Brief transition period\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        currentStage,\n        displayStage,\n        onStageChange\n    ]);\n    // Handle orchestration status changes with color cycling and spinner speed\n    (0,react__WEBPACK_IMPORTED_MODULE_2__.useEffect)({\n        \"DynamicStatusIndicator.useEffect\": ()=>{\n            if (orchestrationStatus && orchestrationStatus !== lastOrchestrationStatus) {\n                console.log(\"\\uD83C\\uDFA8 Orchestration status changed: \".concat(orchestrationStatus));\n                setLastOrchestrationStatus(orchestrationStatus);\n                setOrchestrationColorIndex({\n                    \"DynamicStatusIndicator.useEffect\": (prev)=>prev + 1\n                }[\"DynamicStatusIndicator.useEffect\"]);\n                setIsTransitioning(true);\n                // Speed up spinner on status change\n                setSpinnerSpeed(2.5); // Fast speed during transition\n                // Brief transition animation\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setIsTransitioning(false);\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 300);\n                // Return spinner to normal speed after a delay\n                setTimeout({\n                    \"DynamicStatusIndicator.useEffect\": ()=>{\n                        setSpinnerSpeed(1); // Back to normal speed\n                    }\n                }[\"DynamicStatusIndicator.useEffect\"], 1000);\n            }\n        }\n    }[\"DynamicStatusIndicator.useEffect\"], [\n        orchestrationStatus,\n        lastOrchestrationStatus\n    ]);\n    // Remove auto-progression - let the hook handle stage management\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex justify-start \".concat(className),\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-6 h-6 rounded-full flex items-center justify-center mr-2.5 mt-0.5 flex-shrink-0\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: \"spin \".concat(1.2 / spinnerSpeed, \"s linear infinite\"),\n                            borderTopColor: config.iconColor.replace('text-', ''),\n                            filter: 'drop-shadow(0 0 6px rgba(59, 130, 246, 0.4))'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-2 w-10 h-10 rounded-full border-[3px] border-t-blue-500 border-r-transparent border-b-transparent border-l-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 340,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: \"spin \".concat(1.8 / spinnerSpeed, \"s linear infinite reverse\"),\n                            borderTopColor: config.iconColor.replace('text-', '').replace('600', '400'),\n                            opacity: 0.7\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1.5 w-9 h-9 rounded-full border-[2px] border-t-purple-400 border-r-transparent border-b-transparent border-l-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            animation: \"spin \".concat(0.8 / spinnerSpeed, \"s linear infinite\"),\n                            borderTopColor: config.iconColor.replace('text-', '').replace('600', '300'),\n                            opacity: 0.5\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-1 w-8 h-8 rounded-full border-[1px] border-t-cyan-300 border-r-transparent border-b-transparent border-l-transparent\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 360,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            borderColor: config.iconColor.replace('text-', '').replace('600', '200'),\n                            opacity: 0.3,\n                            animation: 'pulse 2s ease-in-out infinite'\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute -inset-0.5 w-7 h-7 rounded-full border animate-pulse\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 370,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        style: {\n                            boxShadow: \"0 0 12px \".concat(config.iconColor.replace('text-', ''), \"40, 0 0 24px \").concat(config.iconColor.replace('text-', ''), \"20\")\n                        },\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"relative w-full h-full rounded-full flex items-center justify-center transition-all duration-500 \".concat(config.bgColor, \" border-2 \").concat(config.borderColor, \" shadow-lg backdrop-blur-sm\"),\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Icon, {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"w-3.5 h-3.5 transition-all duration-500 \".concat(config.iconColor, \" \").concat(isTransitioning ? 'scale-125 rotate-12' : 'scale-100', \" drop-shadow-lg\")\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 384,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 380,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 338,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"max-w-[65%] rounded-xl px-3 py-2 transition-all duration-500 \".concat(config.bgColor, \" \").concat(config.borderColor, \" border \").concat(config.glowColor, \" shadow-sm backdrop-blur-sm\"),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"flex items-center space-x-1.5\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"transition-all duration-500\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"text-xs font-semibold transition-colors duration-500 \".concat(config.iconColor, \" tracking-wide\"),\n                                    children: orchestrationStatus || config.text\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 393,\n                                    columnNumber: 13\n                                }, this),\n                                isStreaming && displayStage === 'typing' && !orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Live\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 397,\n                                    columnNumber: 15\n                                }, this),\n                                orchestrationStatus && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"ml-1.5 text-[10px] opacity-80 \".concat(config.iconColor, \" font-medium\"),\n                                    children: \"• Orchestrating\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 402,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 392,\n                            columnNumber: 11\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 390,\n                        columnNumber: 9\n                    }, this),\n                    (displayStage === 'generating' || displayStage === 'typing') && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"jsx-f56d70faa8a01b64\" + \" \" + \"mt-2\",\n                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-1 rounded-full overflow-hidden bg-white/30 backdrop-blur-sm border border-white/20\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                style: {\n                                    width: displayStage === 'typing' ? '100%' : '60%',\n                                    animation: displayStage === 'typing' ? 'progressPulse 1.5s ease-in-out infinite, progressShimmer 2s linear infinite' : 'progressShimmer 2s linear infinite, progressGlow 3s ease-in-out infinite'\n                                },\n                                className: \"jsx-f56d70faa8a01b64\" + \" \" + \"h-full rounded-full transition-all duration-1000 bg-gradient-to-r \".concat(config.gradientFrom, \" \").concat(config.gradientTo, \" relative overflow-hidden\"),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    style: {\n                                        animation: 'progressShine 2s linear infinite',\n                                        transform: 'skewX(-20deg)'\n                                    },\n                                    className: \"jsx-f56d70faa8a01b64\" + \" \" + \"absolute inset-0 bg-gradient-to-r from-transparent via-white/40 to-transparent\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                    lineNumber: 423,\n                                    columnNumber: 17\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                                lineNumber: 413,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                            lineNumber: 412,\n                            columnNumber: 13\n                        }, this)\n                    }, void 0, false, {\n                        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                        lineNumber: 411,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n                lineNumber: 389,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)((styled_jsx_style__WEBPACK_IMPORTED_MODULE_1___default()), {\n                id: \"f56d70faa8a01b64\",\n                children: \"@-webkit-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-moz-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-o-keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@keyframes progressShimmer{0%{background-position:-200%0}100%{background-position:200%0}}@-webkit-keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-moz-keyframes progressShine{0%{-moz-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-moz-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-o-keyframes progressShine{0%{-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@keyframes progressShine{0%{-webkit-transform:translatex(-100%)skewx(-20deg);-moz-transform:translatex(-100%)skewx(-20deg);-o-transform:translatex(-100%)skewx(-20deg);transform:translatex(-100%)skewx(-20deg)}100%{-webkit-transform:translatex(300%)skewx(-20deg);-moz-transform:translatex(300%)skewx(-20deg);-o-transform:translatex(300%)skewx(-20deg);transform:translatex(300%)skewx(-20deg)}}@-webkit-keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);transform:scaley(1.1)}}@-moz-keyframes progressPulse{0%,100%{opacity:1;-moz-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-moz-transform:scaley(1.1);transform:scaley(1.1)}}@-o-keyframes progressPulse{0%,100%{opacity:1;-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-o-transform:scaley(1.1);transform:scaley(1.1)}}@keyframes progressPulse{0%,100%{opacity:1;-webkit-transform:scaley(1);-moz-transform:scaley(1);-o-transform:scaley(1);transform:scaley(1)}50%{opacity:.8;-webkit-transform:scaley(1.1);-moz-transform:scaley(1.1);-o-transform:scaley(1.1);transform:scaley(1.1)}}@-webkit-keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-moz-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@-o-keyframes progressGlow{0%,100%{filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}@keyframes progressGlow{0%,100%{-webkit-filter:brightness(1)drop-shadow(0 0 2px currentColor);filter:brightness(1)drop-shadow(0 0 2px currentColor)}50%{-webkit-filter:brightness(1.2)drop-shadow(0 0 6px currentColor);filter:brightness(1.2)drop-shadow(0 0 6px currentColor)}}\"\n            }, void 0, false, void 0, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\RoKey App\\\\rokey-app\\\\src\\\\components\\\\DynamicStatusIndicator.tsx\",\n        lineNumber: 336,\n        columnNumber: 5\n    }, this);\n}\n_s(DynamicStatusIndicator, \"/2+Je50HRbnIv9Z3EeBsB5SYU78=\");\n_c = DynamicStatusIndicator;\n// Hook for managing status progression\nfunction useStatusProgression() {\n    _s1();\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)('initializing');\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_2__.useState)([\n        'initializing'\n    ]);\n    const updateStage = (stage)=>{\n        setCurrentStage(stage);\n        setStageHistory((prev)=>[\n                ...prev,\n                stage\n            ]);\n    };\n    const reset = ()=>{\n        setCurrentStage('initializing');\n        setStageHistory([\n            'initializing'\n        ]);\n    };\n    return {\n        currentStage,\n        stageHistory,\n        updateStage,\n        reset\n    };\n}\n_s1(useStatusProgression, \"7mqPvLO7RfuzcLNJcZduGbqc5yY=\");\nvar _c;\n$RefreshReg$(_c, \"DynamicStatusIndicator\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/components/DynamicStatusIndicator.tsx\n"));

/***/ })

});