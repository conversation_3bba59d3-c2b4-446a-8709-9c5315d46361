"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/playground/page",{

/***/ "(app-pages-browser)/./src/hooks/useMessageStatus.ts":
/*!***************************************!*\
  !*** ./src/hooks/useMessageStatus.ts ***!
  \***************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getStageDescription: () => (/* binding */ getStageDescription),\n/* harmony export */   logStatusPerformance: () => (/* binding */ logStatusPerformance),\n/* harmony export */   useMessageStatus: () => (/* binding */ useMessageStatus),\n/* harmony export */   useSmartMessageStatus: () => (/* binding */ useSmartMessageStatus)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* __next_internal_client_entry_do_not_use__ useMessageStatus,useSmartMessageStatus,getStageDescription,logStatusPerformance auto */ \nconst DEFAULT_STAGE_DURATIONS = {\n    initializing: 50,\n    analyzing: 150,\n    routing: 200,\n    complexity_analysis: 250,\n    role_classification: 300,\n    agent_mode_complexity: 300,\n    agent_mode_selection: 200,\n    agent_mode_collaboration: 500,\n    preparing: 150,\n    connecting: 200,\n    generating: 400,\n    typing: 0,\n    finalizing: 100,\n    complete: 0\n};\nfunction useMessageStatus() {\n    let config = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const { enableAutoProgression = true, stageDurations = {}, onStageChange } = config;\n    const [currentStage, setCurrentStage] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('initializing');\n    const [isActive, setIsActive] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(false);\n    const [stageHistory, setStageHistory] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)([]);\n    const startTimeRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(0);\n    const timersRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    // Merge default durations with custom ones\n    const effectiveDurations = {\n        ...DEFAULT_STAGE_DURATIONS,\n        ...stageDurations\n    };\n    const clearAllTimers = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[clearAllTimers]\": ()=>{\n            timersRef.current.forEach({\n                \"useMessageStatus.useCallback[clearAllTimers]\": (timer)=>clearTimeout(timer)\n            }[\"useMessageStatus.useCallback[clearAllTimers]\"]);\n            timersRef.current = [];\n        }\n    }[\"useMessageStatus.useCallback[clearAllTimers]\"], []);\n    const updateStage = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[updateStage]\": function(stage) {\n            let clearTimers = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : true;\n            const timestamp = Date.now();\n            console.log(\"\\uD83C\\uDFAF Status update: \".concat(stage, \" at \").concat(timestamp));\n            // Special handling for connecting stage - don't clear timers to allow auto-progression\n            if (stage === 'connecting') {\n                console.log('🎯 Connecting stage - keeping auto-progression timers active');\n                setCurrentStage(stage);\n                setStageHistory({\n                    \"useMessageStatus.useCallback[updateStage]\": (prev)=>[\n                            ...prev,\n                            {\n                                stage,\n                                timestamp\n                            }\n                        ]\n                }[\"useMessageStatus.useCallback[updateStage]\"]);\n                onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(stage, timestamp);\n                // Set up progression out of connecting after 2 seconds to show other processes\n                const connectingTimer = setTimeout({\n                    \"useMessageStatus.useCallback[updateStage].connectingTimer\": ()=>{\n                        console.log('🎯 Auto-progressing from connecting to routing after 2s');\n                        setCurrentStage('routing');\n                        setStageHistory({\n                            \"useMessageStatus.useCallback[updateStage].connectingTimer\": (prev)=>[\n                                    ...prev,\n                                    {\n                                        stage: 'routing',\n                                        timestamp: Date.now()\n                                    }\n                                ]\n                        }[\"useMessageStatus.useCallback[updateStage].connectingTimer\"]);\n                        onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange('routing', Date.now());\n                    }\n                }[\"useMessageStatus.useCallback[updateStage].connectingTimer\"], 2000);\n                timersRef.current.push(connectingTimer);\n                return;\n            }\n            // Only clear timers when manually updating (not during auto-progression)\n            if (clearTimers) {\n                clearAllTimers();\n            }\n            setCurrentStage(stage);\n            setStageHistory({\n                \"useMessageStatus.useCallback[updateStage]\": (prev)=>[\n                        ...prev,\n                        {\n                            stage,\n                            timestamp\n                        }\n                    ]\n            }[\"useMessageStatus.useCallback[updateStage]\"]);\n            onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange(stage, timestamp);\n        }\n    }[\"useMessageStatus.useCallback[updateStage]\"], [\n        onStageChange,\n        clearAllTimers\n    ]);\n    const startProcessing = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[startProcessing]\": ()=>{\n            console.log('🎯 Starting processing - dynamic progression with realistic random timing');\n            setIsActive(true);\n            startTimeRef.current = Date.now();\n            setStageHistory([\n                {\n                    stage: 'initializing',\n                    timestamp: Date.now()\n                }\n            ]);\n            setCurrentStage('initializing');\n            // Helper function to add realistic randomness to timing\n            const randomDelay = {\n                \"useMessageStatus.useCallback[startProcessing].randomDelay\": function(baseMs) {\n                    let variationPercent = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 30;\n                    const variation = baseMs * (variationPercent / 100);\n                    const randomOffset = (Math.random() - 0.5) * 2 * variation;\n                    return Math.max(200, Math.round(baseMs + randomOffset)); // Minimum 200ms\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].randomDelay\"];\n            // Dynamic progression with realistic random timing\n            let cumulativeTime = 0;\n            // Analyzing: 600-1200ms (simulating message parsing)\n            cumulativeTime += randomDelay(900, 35);\n            const timer1 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer1\": ()=>{\n                    console.log('🎯 Auto-progressing to analyzing');\n                    updateStage('analyzing', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer1\"], cumulativeTime);\n            // Complexity Analysis: 800-1600ms (simulating AI complexity assessment)\n            cumulativeTime += randomDelay(1200, 40);\n            const timer2 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer2\": ()=>{\n                    console.log('🎯 Auto-progressing to complexity_analysis');\n                    updateStage('complexity_analysis', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer2\"], cumulativeTime);\n            // Role Classification: 1000-2000ms (simulating intelligent role matching)\n            cumulativeTime += randomDelay(1500, 35);\n            const timer3 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer3\": ()=>{\n                    console.log('🎯 Auto-progressing to role_classification');\n                    updateStage('role_classification', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer3\"], cumulativeTime);\n            // Preparing: 600-1400ms (simulating model preparation)\n            cumulativeTime += randomDelay(1000, 40);\n            const timer4 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer4\": ()=>{\n                    console.log('🎯 Auto-progressing to preparing');\n                    updateStage('preparing', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer4\"], cumulativeTime);\n            // Connecting: 800-1600ms (simulating network connection)\n            cumulativeTime += randomDelay(1200, 35);\n            const timer5 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer5\": ()=>{\n                    console.log('🎯 Auto-progressing to connecting');\n                    updateStage('connecting', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer5\"], cumulativeTime);\n            // Routing: 1000-2000ms (simulating API key selection and routing)\n            cumulativeTime += randomDelay(1500, 40);\n            const timer6 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer6\": ()=>{\n                    console.log('🎯 Auto-progressing to routing after connecting');\n                    updateStage('routing', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer6\"], cumulativeTime);\n            // Fallback to generating: 800-1600ms (final preparation)\n            cumulativeTime += randomDelay(1200, 35);\n            const timer7 = setTimeout({\n                \"useMessageStatus.useCallback[startProcessing].timer7\": ()=>{\n                    console.log('🎯 Fallback to generating');\n                    updateStage('generating', false);\n                }\n            }[\"useMessageStatus.useCallback[startProcessing].timer7\"], cumulativeTime);\n            console.log(\"\\uD83C\\uDFAF Total estimated processing time: \".concat(cumulativeTime, \"ms (\").concat((cumulativeTime / 1000).toFixed(1), \"s)\"));\n            timersRef.current.push(timer1, timer2, timer3, timer4, timer5, timer6, timer7);\n        }\n    }[\"useMessageStatus.useCallback[startProcessing]\"], [\n        onStageChange,\n        updateStage\n    ]);\n    const markStreaming = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[markStreaming]\": ()=>{\n            console.log('🎯 markStreaming called - switching to typing');\n            clearAllTimers();\n            const timestamp = Date.now();\n            setCurrentStage('typing');\n            setStageHistory({\n                \"useMessageStatus.useCallback[markStreaming]\": (prev)=>[\n                        ...prev,\n                        {\n                            stage: 'typing',\n                            timestamp\n                        }\n                    ]\n            }[\"useMessageStatus.useCallback[markStreaming]\"]);\n            onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange('typing', timestamp);\n        }\n    }[\"useMessageStatus.useCallback[markStreaming]\"], [\n        clearAllTimers,\n        onStageChange\n    ]);\n    const markComplete = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[markComplete]\": ()=>{\n            clearAllTimers();\n            updateStage('complete');\n            setIsActive(false);\n        }\n    }[\"useMessageStatus.useCallback[markComplete]\"], [\n        clearAllTimers,\n        updateStage\n    ]);\n    const markOrchestrationStarted = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[markOrchestrationStarted]\": ()=>{\n            console.log('🎭 markOrchestrationStarted called - switching to orchestration mode');\n            clearAllTimers();\n            const timestamp = Date.now();\n            setCurrentStage('generating'); // Use generating as base stage for orchestration\n            setStageHistory({\n                \"useMessageStatus.useCallback[markOrchestrationStarted]\": (prev)=>[\n                        ...prev,\n                        {\n                            stage: 'generating',\n                            timestamp\n                        }\n                    ]\n            }[\"useMessageStatus.useCallback[markOrchestrationStarted]\"]);\n            onStageChange === null || onStageChange === void 0 ? void 0 : onStageChange('generating', timestamp);\n        }\n    }[\"useMessageStatus.useCallback[markOrchestrationStarted]\"], [\n        clearAllTimers,\n        onStageChange\n    ]);\n    const updateOrchestrationStatus = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[updateOrchestrationStatus]\": (status)=>{\n            // This will be used to update the status text dynamically\n            // For now, keep the stage as 'generating' but we could extend this\n            console.log('🎭 Orchestration status update:', status);\n        }\n    }[\"useMessageStatus.useCallback[updateOrchestrationStatus]\"], []);\n    const reset = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[reset]\": ()=>{\n            clearAllTimers();\n            setCurrentStage('initializing');\n            setIsActive(false);\n            setStageHistory([]);\n            startTimeRef.current = 0;\n        }\n    }[\"useMessageStatus.useCallback[reset]\"], [\n        clearAllTimers\n    ]);\n    const getProcessingDuration = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useMessageStatus.useCallback[getProcessingDuration]\": ()=>{\n            if (startTimeRef.current === 0) return 0;\n            return Date.now() - startTimeRef.current;\n        }\n    }[\"useMessageStatus.useCallback[getProcessingDuration]\"], []);\n    // Cleanup on unmount\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useMessageStatus.useEffect\": ()=>{\n            return ({\n                \"useMessageStatus.useEffect\": ()=>{\n                    clearAllTimers();\n                }\n            })[\"useMessageStatus.useEffect\"];\n        }\n    }[\"useMessageStatus.useEffect\"], [\n        clearAllTimers\n    ]);\n    return {\n        currentStage,\n        isActive,\n        stageHistory,\n        startProcessing,\n        updateStage,\n        markStreaming,\n        markComplete,\n        markOrchestrationStarted,\n        updateOrchestrationStatus,\n        reset,\n        getProcessingDuration\n    };\n}\n// Enhanced hook that can detect backend stages from response headers or logs\nfunction useSmartMessageStatus() {\n    let config = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : {};\n    const baseStatus = useMessageStatus(config);\n    const [detectedStages, setDetectedStages] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)(new Set());\n    // Function to analyze response headers and show appropriate status based on actual backend processes\n    const analyzeResponseHeaders = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSmartMessageStatus.useCallback[analyzeResponseHeaders]\": (headers)=>{\n            console.log('🎯 Backend response received - immediately showing generating stage');\n            // Look for custom headers that indicate what processes actually ran\n            const roleUsed = headers.get('x-rokey-role-used');\n            const routingStrategy = headers.get('x-rokey-routing-strategy');\n            const complexity = headers.get('x-rokey-complexity-level');\n            const provider = headers.get('x-rokey-api-key-provider');\n            console.log('🎯 Headers found:', {\n                roleUsed,\n                routingStrategy,\n                complexity,\n                provider\n            });\n            // Immediately show generating since backend is about to start streaming\n            console.log('🎯 Backend response received - showing generating (AI is working)');\n            baseStatus.updateStage('generating');\n        }\n    }[\"useSmartMessageStatus.useCallback[analyzeResponseHeaders]\"], [\n        baseStatus\n    ]);\n    // Function to analyze streaming chunks for status indicators\n    const analyzeStreamChunk = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSmartMessageStatus.useCallback[analyzeStreamChunk]\": (chunk)=>{\n            // Look for backend log patterns in the stream\n            if (chunk.includes('[Complexity Classification]') && !detectedStages.has('complexity')) {\n                baseStatus.updateStage('complexity_analysis');\n                setDetectedStages({\n                    \"useSmartMessageStatus.useCallback[analyzeStreamChunk]\": (prev)=>new Set([\n                            ...prev,\n                            'complexity'\n                        ])\n                }[\"useSmartMessageStatus.useCallback[analyzeStreamChunk]\"]);\n            }\n            if (chunk.includes('[Intelligent Role Strategy]') && !detectedStages.has('role')) {\n                baseStatus.updateStage('role_classification');\n                setDetectedStages({\n                    \"useSmartMessageStatus.useCallback[analyzeStreamChunk]\": (prev)=>new Set([\n                            ...prev,\n                            'role'\n                        ])\n                }[\"useSmartMessageStatus.useCallback[analyzeStreamChunk]\"]);\n            }\n            if (chunk.includes('FIRST TOKEN:') && !detectedStages.has('streaming')) {\n                baseStatus.markStreaming();\n                setDetectedStages({\n                    \"useSmartMessageStatus.useCallback[analyzeStreamChunk]\": (prev)=>new Set([\n                            ...prev,\n                            'streaming'\n                        ])\n                }[\"useSmartMessageStatus.useCallback[analyzeStreamChunk]\"]);\n            }\n        }\n    }[\"useSmartMessageStatus.useCallback[analyzeStreamChunk]\"], [\n        baseStatus,\n        detectedStages\n    ]);\n    const resetWithCleanup = (0,react__WEBPACK_IMPORTED_MODULE_0__.useCallback)({\n        \"useSmartMessageStatus.useCallback[resetWithCleanup]\": ()=>{\n            baseStatus.reset();\n            setDetectedStages(new Set());\n        }\n    }[\"useSmartMessageStatus.useCallback[resetWithCleanup]\"], [\n        baseStatus\n    ]);\n    return {\n        ...baseStatus,\n        analyzeResponseHeaders,\n        analyzeStreamChunk,\n        reset: resetWithCleanup,\n        detectedStages: Array.from(detectedStages)\n    };\n}\n// Utility function to get user-friendly stage descriptions\nfunction getStageDescription(stage, context) {\n    const baseDescriptions = {\n        initializing: 'Getting ready to process your request',\n        analyzing: 'Understanding your message and requirements',\n        routing: 'Finding the best AI model for your task',\n        complexity_analysis: \"Analyzing task complexity\".concat((context === null || context === void 0 ? void 0 : context.complexity) ? \" (Level \".concat(context.complexity, \")\") : ''),\n        role_classification: \"Selecting AI specialist\".concat((context === null || context === void 0 ? void 0 : context.role) ? \" (\".concat(context.role, \")\") : ''),\n        preparing: 'Setting up the AI model for your request',\n        connecting: 'Establishing connection to the AI service',\n        generating: 'AI is thinking and crafting your response',\n        typing: 'Streaming the response to you in real-time',\n        finalizing: 'Putting the finishing touches on the response',\n        complete: 'Response delivered successfully'\n    };\n    return baseDescriptions[stage];\n}\n// Performance monitoring utilities\nfunction logStatusPerformance(stageHistory) {\n    if (stageHistory.length < 2) return;\n    console.group('🎯 Message Status Performance');\n    for(let i = 1; i < stageHistory.length; i++){\n        const current = stageHistory[i];\n        const previous = stageHistory[i - 1];\n        const duration = current.timestamp - previous.timestamp;\n        console.log(\"\".concat(previous.stage, \" → \").concat(current.stage, \": \").concat(duration, \"ms\"));\n    }\n    const totalDuration = stageHistory[stageHistory.length - 1].timestamp - stageHistory[0].timestamp;\n    console.log(\"Total processing time: \".concat(totalDuration, \"ms\"));\n    console.groupEnd();\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./src/hooks/useMessageStatus.ts\n"));

/***/ })

});